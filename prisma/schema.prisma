//  ─── Generators ────────────────────────────────────────────────────────────
generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

generator zod {
  provider = "prisma-zod-generator"
  output   = "../src/lib/zod"
}

//  ─── Data<PERSON>urce ────────────────────────────────────────────────────────────
datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
  schemas   = ["public"]
}

//==========================================================================
//                              ENUMS
//==========================================================================

/// @description Defines the user roles within the application.
enum Role {
  ACCOUNT_HOLDER
  BROKER
  ADMIN

  @@map("role")
  @@schema("public")
}

/// @description KYC (Know Your Customer) verification status for brokers.
enum KYCStatus {
  PENDING
  VERIFIED
  REJECTED

  @@map("kyc_status")
  @@schema("public")
}

/// @description The frequency of policy premium payments.
enum PaymentPeriod {
  MONTHLY
  QUARTERLY
  SEMIANNUAL
  ANNUAL

  @@map("payment_period")
  @@schema("public")
}

/// @description The fuel type of the insured vehicle.
enum FuelType {
  GASOLINE
  DIESEL
  ELECTRIC
  HYBRID

  @@map("fuel_type")
  @@schema("public")
}

/// @description The type of garage where the vehicle is typically parked.
enum GarageType {
  STREET
  SHARED_UNGUARDED
  SHARED_GUARDED
  PRIVATE

  @@map("garage_type")
  @@schema("public")
}

/// @description The primary usage of the insured vehicle.
enum UsageType {
  PRIVATE_OCCASIONAL
  PRIVATE_REGULAR
  PROFESSIONAL_OCCASIONAL
  PROFESSIONAL_REGULAR

  @@map("usage_type")
  @@schema("public")
}

/// @description The estimated annual kilometer range for the vehicle.
enum KmRange {
  UP_TO_2000
  FROM_2000_TO_4000
  FROM_4000_TO_6000
  FROM_6000_TO_8000
  FROM_8000_TO_10000
  FROM_10000_TO_12000
  FROM_12000_TO_14000
  FROM_14000_TO_16000
  FROM_16000_TO_18000
  FROM_18000_TO_20000
  FROM_20000_TO_22000
  FROM_22000_TO_24000
  FROM_24000_TO_26000
  FROM_26000_TO_28000
  FROM_28000_TO_30000
  FROM_30000_TO_32000
  FROM_32000_TO_34000
  FROM_34000_TO_36000
  FROM_36000_TO_38000
  FROM_38000_TO_40000
  FROM_40000_TO_45000
  FROM_45000_TO_50000
  OVER_50000

  @@map("km_range")
  @@schema("public")
}

/// @description Generic asset classification.
enum AssetType {
  CAR
  MOTORCYCLE
  PROPERTY
  PERSON
  OTHER

  @@map("asset_type")
  @@schema("public")
}

// Shortened list of Spanish provinces for brevity. Extend as needed.
/// @description Spanish provinces (and autonomous cities).
enum Province {
  // Andalucía
  ALMERIA
  CADIZ
  CORDOBA
  GRANADA
  HUELVA
  JAEN
  MALAGA
  SEVILLA

  // Aragón
  HUESCA
  TERUEL
  ZARAGOZA

  // Asturias
  ASTURIAS

  // Baleares
  BALEARES

  // Canarias
  LAS_PALMAS
  SANTA_CRUZ_DE_TENERIFE

  // Cantabria
  CANTABRIA

  // Castilla-La Mancha
  ALBACETE
  CIUDAD_REAL
  CUENCA
  GUADALAJARA
  TOLEDO

  // Castilla y León
  AVILA
  BURGOS
  LEON
  PALENCIA
  SALAMANCA
  SEGOVIA
  SORIA
  VALLADOLID
  ZAMORA

  // Cataluña
  BARCELONA
  GIRONA
  LLEIDA
  TARRAGONA

  // Comunidad Valenciana
  ALICANTE
  CASTELLON
  VALENCIA

  // Extremadura
  BADAJOZ
  CACERES

  // Galicia
  A_CORUNA
  LUGO
  OURENSE
  PONTEVEDRA

  // La Rioja
  LA_RIOJA

  // Madrid
  MADRID

  // Murcia
  MURCIA

  // Navarra
  NAVARRA

  // País Vasco
  ALAVA
  GIPUZKOA
  BIZKAIA

  // Ceuta y Melilla
  CEUTA
  MELILLA

  @@map("province")
  @@schema("public")
}

/// @description Spanish autonomous communities.
enum Region {
  ANDALUSIA
  ARAGON
  ASTURIAS
  BALEARES
  BASQUE_COUNTRY
  CANARY_ISLANDS
  CANTABRIA
  CASTILLA_LA_MANCHA
  CASTILLA_Y_LEON
  CATALONIA
  EXTREMADURA
  GALICIA
  LA_RIOJA
  MADRID
  MURCIA
  NAVARRE
  VALENCIAN_COMMUNITY
  CEUTA
  MELILLA

  @@map("region")
  @@schema("public")
}

/// @description Country list (initially only Spain).
enum Country {
  SPAIN

  @@map("country")
  @@schema("public")
}

/// @description Types of documents that can be uploaded by account holders and Brokers.
enum DocumentType {
  // Policy-related documents
  POLICY_DOCUMENT
  QUOTE_DOCUMENT
  INSURANCE_CERTIFICATE
  CLAIM_DOCUMENT

  // Identity documents
  ID_FRONT
  ID_BACK
  ID_DOC
  PASSPORT
  FACE_MATCH_SELFIE

  // Professional documents
  EMPLOYMENT_CONTRACT
  BROKER_LICENSE
  PROFESSIONAL_CERTIFICATE

  // Vehicle documents
  VEHICLE_REGISTRATION

  // Medical documents
  MEDICAL_REPORT

  // Repair documents
  REPAIR_ESTIMATE

  // Other
  OTHER

  @@map("document_type")
  @@schema("public")
}

/// @description The type of guarantee or coverage.
// (unchanged for brevity – kept identical to original)
enum GuaranteeType {
  ADVANCE_COMPENSATION
  ASSISTIVE_EQUIPMENT_RENTAL
  COLLISION_WITH_ANIMALS
  DRIVER_ACCIDENTS
  EXTRAORDINARY_RISKS_PERSONS
  EXTRAORDINARY_RISKS_VEHICLE
  FINES_MANAGEMENT
  FIRE
  GLASS_BREAKAGE
  HANDYMAN_SERVICE
  IMMOBILIZATION
  LEGAL_DEFENSE
  LEGAL_REPRESENTATION_EXTENSION
  LICENSE_SUSPENSION
  LICENSE_SUSPENSION_SUBSIDY
  LOAD_LIABILITY
  LOST_KEYS
  MANDATORY_LIABILITY
  OTHER
  PERSONAL_BELONGINGS
  PSYCHOLOGICAL_ASSISTANCE
  REPATRIATION
  THEFT
  TOTAL_LOSS_DAMAGE
  TOTAL_LOSS_FIRE
  TOTAL_LOSS_THEFT
  TOWING_FROM_KM0
  TRAVEL_ASSISTANCE
  VEHICLE_DAMAGE
  VEHICLE_REPLACEMENT
  VOLUNTARY_LIABILITY
  WEATHER_DAMAGE
  TYRE_DAMAGE
  MOTORCYCLE_GEAR
  NON_STANDARD_ACCESSORIES
  PET_INJURY
  PASSENGER_ACCIDENTS
  GAP_COVERAGE
  PARALYZATION_COMPENSATION
  CARGO_LIABILITY
  UNAUTHORIZED_USE
  ERROR_REFUELING
  CHARGING_CABLE

  @@map("guarantee_type")
  @@schema("public")
}

/// @description The role of a party in relation to the policy.
enum PartyRole {
  POLICYHOLDER
  OWNER
  MAIN_DRIVER
  ADDITIONAL_DRIVER

  @@map("party_role")
  @@schema("public")
}

/// @description The lifecycle status of an insurance policy.
enum PolicyStatus {
  DRAFT
  ACTIVE
  RENEW_SOON
  EXPIRED
  REJECTED

  @@map("policy_status")
  @@schema("public")
}

/// @description The type of insurance policy.
enum PolicyType {
  THIRD_PARTY
  THIRD_PARTY_EXTENDED
  COMPREHENSIVE
  COMPREHENSIVE_WITH_DEDUCTIBLE

  @@map("policy_type")
  @@schema("public")
}

/// @description Defines the possible states of an auction.
enum AuctionState {
  OPEN
  CLOSED
  AWARDED
  DEAL_CONFIRMED
  CANCELED
  EXPIRED

  @@map("auction_state")
  @@schema("public")
}

/// @description Defines the gender of an individual.
enum Gender {
  MALE
  FEMALE

  @@map("gender")
  @@schema("public")
}

/// @description Defines the most popular insurance companies in Spain.
enum InsurerCompany {
  MAPFRE
  ALLIANZ
  AXA
  GENERALI
  SANTALUCIA
  MUTUA_MADRILENA
  DIRECT_SEGUROS
  LINEA_DIRECTA
  REALE_SEGUROS
  ZURICH
  CATALANA_OCCIDENTE
  DKV
  FIATC
  HELVETIA
  PLUS_ULTRA
  AEGON
  QUALITAS_AUTO
  BALOISE
  PELAYO
  MMT_SEGUROS
  NATIONALE_NEDERLANDEN
  LIBERTY_SEGUROS
  ADESLAS
  ASISA
  SANITAS
  CASER
  OCASO
  ARAG
  EUROP_ASSISTANCE
  INTERMUTUAS
  MGS_SEGUROS
  SEGURCAIXA_ADESLAS
  VERTI
  GENESIS
  OTRAS

  @@map("insurer_company")
  @@schema("public")
}


//==========================================================================
//                              MODELS
//==========================================================================

// ──────────────────────────────────────────────────────────────────────────
//  USER & ROLE-SPECIFIC PROFILES
// ──────────────────────────────────────────────────────────────────────────

/// @description Stores authentication data, role, and basic profile information mirrored from Supabase `auth.users`.
/// A user can have **one** auxiliary profile of each kind (Account-Holder, Broker, Admin).
model User {
  // ── Identification ─────────────────────────────────────────────────────
  id    String  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  email String  @unique
  phone String? @unique

  // ── Personal names (mirrored) ──────────────────────────────────────────
  firstName   String? @map("first_name")
  lastName    String? @map("last_name")
  displayName String? @map("display_name")

  // ── Role & timestamps ─────────────────────────────────────────────────
  role      Role
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // ── Auxiliary role-specific 1-to-1 profiles ───────────────────────────
  accountHolderProfile AccountHolderProfile?
  brokerProfile        BrokerProfile?
  adminProfile         AdminProfile?

  // ── Indexes ───────────────────────────────────────────────────────────
  @@index([email])
  @@index([displayName])
  @@map("user")
  @@schema("public")
}

/// @description Profile information for an account holder (policy owner).
model AccountHolderProfile {
  // ── Identification ────────────────────────────────────────────────────
  id     String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId String @unique @map("user_id") @db.Uuid

  // ── Relations ────────────────────────────────────────────────────────
  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  policies       Policy[]        @relation("PolicyUploader") // Policies uploaded
  insuredParties InsuredParty[]
  assets         Asset[]
  auctions       Auction[]       @relation("AccountHolderAuctions")
  documents      Documentation[]

  // ── Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // ── Indexes ──────────────────────────────────────────────────────────
  @@index([userId])
  @@map("account_holder_profile")
  @@schema("public")
}

/// @description Administrator profile information.
model AdminProfile {
  // ── Identification ────────────────────────────────────────────────────
  id     String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId String @unique @map("user_id") @db.Uuid

  // ── Relations ────────────────────────────────────────────────────────
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  // ── Timestamps ───────────────────────────────────────────────────────
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

 // ── Indexes ──────────────────────────────────────────────────────────
  @@map("admin_profile")
  @@schema("public")
}

/// @description Profile and professional details for an insurance broker.
model BrokerProfile {
  // ── Identification & Registration ─────────────────────────────────────
  id                String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId            String   @unique @map("user_id") @db.Uuid
  registrationClass String   @map("registration_class")
  registrationKey   String   @unique @map("registration_key")
  registrationDate  DateTime @map("registration_date")
  legalName         String   @map("legal_name")
  identifier        String   @unique

  // ── Flags ────────────────────────────────────────────────────────────
  isAuthorizedByOther Boolean   @map("is_authorized_by_other")
  isComplementary     Boolean   @map("is_complementary")
  isGroupAgent        Boolean   @map("is_group_agent")
  kycStatus           KYCStatus @default(PENDING) @map("kyc_status")

  // ── Relations ────────────────────────────────────────────────────────
  user           User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  bids           Bid[]               @relation("BrokerBids")
  subscriptions  Subscription[]
  leadPayments   AuctionCommission[]
  winningBids    AuctionWinner[]
  billingAddress Address?            @relation("BrokerBillingAddress")
  documents      Documentation[]     @relation("BrokerDocuments")

  // ── Stripe ───────────────────────────────────────────────────────────
  stripeCustomerId String? @unique @map("stripe_customer_id")

  // ── Timestamps ─────────────────────────────────────────────
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

 // ── Indexes ──────────────────────────────────────────────────────────
  @@index([userId])
  @@index([registrationKey])
  @@index([identifier])
  @@map("broker_profile")
  @@schema("public")
}

// ──────────────────────────────────────────────────────────────────────────
//  SUBSCRIPTIONS & BILLING
// ──────────────────────────────────────────────────────────────────────────

/// @description Manages broker subscriptions via Stripe.
model Subscription {
  // ── Identification ────────────────────────────────────────────────────
  id                   String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  brokerId             String @map("broker_id") @db.Uuid
  stripeSubscriptionId String @unique @map("stripe_subscription_id")

  // ── Status & Period ──────────────────────────────────────────────────
  status           String
  currentPeriodEnd DateTime @map("current_period_end")

  // ── Relations ────────────────────────────────────────────────────────
  broker BrokerProfile @relation(fields: [brokerId], references: [id])

  // ── Timestamps & Indexes ─────────────────────────────────────────────
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

 // ── Indexes ──────────────────────────────────────────────────────────
  @@index([brokerId])
  @@map("broker_subscription")
  @@schema("public")
}

// ──────────────────────────────────────────────────────────────────────────
//  ADDRESS & ID VERIFICATION
// ──────────────────────────────────────────────────────────────────────────

/// @description Generic address model (used by brokers & insured parties).
model Address {
  // ── Identification ────────────────────────────────────────────────────
  id String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid

  // ── Owner FK (one of) ────────────────────────────────────────────────
  brokerId       String? @unique @map("broker_id") @db.Uuid
  insuredPartyId String? @unique @map("insured_party_id") @db.Uuid

  // ── Address fields ───────────────────────────────────────────────────
  street     String   @map("address")
  city       String
  province   Province @map("province")
  region     Region   @map("region")
  country    Country  @default(SPAIN) @map("country")
  postalCode String   @map("postal_code")

  // ── Relations ────────────────────────────────────────────────────────
  broker       BrokerProfile? @relation("BrokerBillingAddress", fields: [brokerId], references: [id], onDelete: Cascade)
  insuredParty InsuredParty?  @relation("InsuredPartyAddress", fields: [insuredPartyId], references: [id], onDelete: Cascade)

  // ── Indexes ──────────────────────────────────────────────────────────
  @@index([brokerId])
  @@index([insuredPartyId])
  @@map("address")
  @@schema("public")
}

// ──────────────────────────────────────────────────────────────────────────
//  DOCUMENTATION (flexible uploads)
// ──────────────────────────────────────────────────────────────────────────

/// @description Unified document storage for both brokers and account holders.
model Documentation {
  // ── Identification ────────────────────────────────────────────────────
  id String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid

  // ── Owner FK (one of) ────────────────────────────────────────────────
  accountHolderId String? @map("account_holder_id") @db.Uuid
  brokerId        String? @map("broker_id") @db.Uuid

  // ── Related entities ─────────────────────────────────────────────────
  relatedAuctionId String? @map("related_auction_id") @db.Uuid

  // ── Document info ────────────────────────────────────────────────────
  type        DocumentType
  url         String
  fileName    String?      @map("file_name")
  fileSize    Int?         @map("file_size")
  mimeType    String?      @map("mime_type")

  // ── Relations ────────────────────────────────────────────────────────
  accountHolder  AccountHolderProfile? @relation(fields: [accountHolderId], references: [id], onDelete: Cascade)
  broker         BrokerProfile?        @relation("BrokerDocuments", fields: [brokerId], references: [id], onDelete: Cascade)
  relatedAuction Auction?              @relation("AuctionDocuments", fields: [relatedAuctionId], references: [id], onDelete: Cascade)

  // ── Document references ──────────────────────────────────────────────
  policyDocuments Policy[] @relation("PolicyDocument")
  bidDocuments    Bid[]    @relation("BidDocument")

  // ── Timestamps  ─────────────────────────────────────────────
  uploadedAt DateTime @default(now()) @map("uploaded_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

 // ── Indexes ──────────────────────────────────────────────────────────
  @@index([accountHolderId])
  @@index([brokerId])
  @@index([type])
  @@index([relatedAuctionId])

  @@map("documentation")
  @@schema("public")
}

// ──────────────────────────────────────────────────────────────────────────
//  ASSETS & VEHICLES
// ──────────────────────────────────────────────────────────────────────────

/// @description Detailed information about an insured asset (e.g., car, motorcycle).
model Asset {
  // ── Identification ────────────────────────────────────────────────────
  id              String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  accountHolderId String @map("account_holder_id") @db.Uuid

  // ── Asset details ────────────────────────────────────────────────────
  assetType   AssetType @map("asset_type")
  description String?
  value       Decimal?

  // ── Relations ────────────────────────────────────────────────────────
  accountHolder  AccountHolderProfile @relation(fields: [accountHolderId], references: [id])
  vehicleDetails Vehicle?
  policies       Policy[]

  // ── Indexes ──────────────────────────────────────────────────────────
  @@index([accountHolderId])
  @@map("asset")
  @@schema("public")
}

/// @description Vehicle-specific fields for an asset.
model Vehicle {
  // ── Identification ────────────────────────────────────────────────────
  id      String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  assetId String @unique @map("asset_id") @db.Uuid

  // ── Vehicle attributes ───────────────────────────────────────────────
  licensePlate          String?     @unique @map("license_plate")
  brand                 String?
  model                 String?
  version               String?
  year                  Int?
  firstRegistrationDate DateTime?   @map("first_registration_date")
  fuelType              FuelType?   @map("fuel_type")
  powerCv               Float?      @map("power_cv")
  garageType            GarageType? @map("garage_type")
  usageType             UsageType?  @map("usage_type")
  chassisNumber         String?     @unique @map("chassis_number")
  kmPerYear             KmRange?    @map("km_per_year")
  isLeased              Boolean?    @map("is_leased")
  seats                 Int?

  // ── Relations ────────────────────────────────────────────────────────
  asset Asset @relation(fields: [assetId], references: [id], onDelete: Cascade)

 // ── Indexes ──────────────────────────────────────────────────────────
  @@map("vehicle")
  @@schema("public")
}

// ──────────────────────────────────────────────────────────────────────────
//  INSURED PARTIES
// ──────────────────────────────────────────────────────────────────────────

/// @description Represents an individual or entity covered by a policy.
model InsuredParty {
  // ── Identification ────────────────────────────────────────────────────
  id              String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  accountHolderId String @map("account_holder_id") @db.Uuid

  // ── Personal info ────────────────────────────────────────────────────
  role                  PartyRole
  firstName             String?   @map("first_name")
  lastName              String?   @map("last_name")
  displayName           String?   @map("display_name")
  identification        String    @unique
  gender                Gender?
  birthDate             DateTime? @map("birth_date")
  driverLicenseNumber   String?   @map("driver_license_number")
  driverLicenseIssuedAt DateTime? @map("driver_license_issued_at")

  // ── Relations ────────────────────────────────────────────────────────
  accountHolder AccountHolderProfile @relation(fields: [accountHolderId], references: [id])
  address       Address?             @relation("InsuredPartyAddress")
  policies      PolicyInsuredParty[]

  // ── Timestamps ─────────────────────────────────────────────
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // ── Indexes ──────────────────────────────────────────────────────────
  @@index([identification])
  @@index([accountHolderId])
  @@map("insured_party")
  @@schema("public")
}

// ──────────────────────────────────────────────────────────────────────────
//  POLICY & COVERAGE
// ──────────────────────────────────────────────────────────────────────────

/// @description Represents a user's insurance policy document and its extracted data.
model Policy {
  // ── Identification ────────────────────────────────────────────────────
  id String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid

  // ── Ownership & Relations ────────────────────────────────────────────
  accountHolderId String? @map("account_holder_id") @db.Uuid
  assetId         String? @map("asset_id") @db.Uuid
  accountHolder  AccountHolderProfile? @relation("PolicyUploader", fields: [accountHolderId], references: [id])
  asset          Asset?                @relation(fields: [assetId], references: [id])
  document       Documentation?        @relation("PolicyDocument", fields: [documentId], references: [id])
  coverages      Coverage[]
  insuredParties PolicyInsuredParty[]
  auction        Auction?

  // ── Policy details ───────────────────────────────────────────────────
  documentId            String?        @map("document_id") @db.Uuid
  policyNumber          String?        @unique @map("policy_number")
  insurerCompany        InsurerCompany? @map("insurer_company")
  status                PolicyStatus   @default(DRAFT)
  isAssetsTypeConfirmed Boolean        @default(false) @map("is_assets_type_confirmed")
  startDate             DateTime?      @map("start_date")
  endDate               DateTime?      @map("end_date")
  paymentPeriod         PaymentPeriod? @map("payment_period")
  premium               Decimal?
  productName           String?        @map("product_name")

  // ── Timestamps ─────────────────────────────────────────────
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

 // ── Indexes ──────────────────────────────────────────────────────────
  @@index([accountHolderId])
  @@index([assetId])
  @@index([documentId])
  @@index([status])
  @@map("policy")
  @@schema("public")
}

/// @description Defines a specific coverage within an insurance policy.
model Coverage {
  // ── Identification ────────────────────────────────────────────────────
  id       String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  policyId String @map("policy_id") @db.Uuid

  // ── Coverage details ─────────────────────────────────────────────────
  type        GuaranteeType
  customName  String?       @map("custom_name")
  limit       Decimal?
  deductible  Decimal?
  description String?

  // ── Relations ────────────────────────────────────────────────────────
  policy Policy @relation(fields: [policyId], references: [id], onDelete: Cascade)

  // ── Indexes ──────────────────────────────────────────────────────────
  @@index([policyId])
  @@map("policy_coverage")
  @@schema("public")
}

/// @description Join table linking policies and insured parties (many-to-many).
model PolicyInsuredParty {
    // ── Identification ────────────────────────────────────────────────────
  policyId       String @map("policy_id") @db.Uuid
  insuredPartyId String @map("insured_party_id") @db.Uuid

  // ── Relations ────────────────────────────────────────────────────────
  policy       Policy       @relation(fields: [policyId], references: [id], onDelete: Cascade)
  insuredParty InsuredParty @relation(fields: [insuredPartyId], references: [id], onDelete: Cascade)

  // ── Indexes ──────────────────────────────────────────────────────────
  @@id([policyId, insuredPartyId], map: "policy_insured_party_pkey")
  @@index([insuredPartyId])
  @@map("policy_insured_party")
  @@schema("public")
}

// ──────────────────────────────────────────────────────────────────────────
//  AUCTIONS & BIDS
// ──────────────────────────────────────────────────────────────────────────

/// @description A reverse auction for an insurance policy where brokers compete for leads.
model Auction {
  // ── Identification ────────────────────────────────────────────────────
  id              String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  accountHolderId String @map("account_holder_id") @db.Uuid
  policyId        String @unique @map("policy_id") @db.Uuid

  // ── Schedule & Status ─────────────────────────────────────────────────
  startDate            DateTime     @map("start_date")
  endDate              DateTime     @map("end_date")
  workingHoursClosedAt DateTime     @map("working_hours_closed_at")
  status               AuctionState @default(OPEN)
  maxWinners           Int          @default(3) @map("max_winners")
  minWinners           Int          @default(1) @map("min_winners")
  cancellationReason   String?      @map("cancellation_reason")

  // ── Relations ────────────────────────────────────────────────────────
  accountHolder AccountHolderProfile @relation("AccountHolderAuctions", fields: [accountHolderId], references: [id], onDelete: Cascade)
  policy        Policy               @relation(fields: [policyId], references: [id])
  documents     Documentation[]      @relation("AuctionDocuments")
  bids          Bid[]                @relation("AuctionBids")
  winners       AuctionWinner[]
  commissions   AuctionCommission[]

  // ── Timestamps ─────────────────────────────────────────────
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

 // ── Indexes ──────────────────────────────────────────────────────────
  @@index([accountHolderId])
  @@map("auction")
  @@schema("public")
}

/// @description A bid placed by a broker on a policy auction.
model Bid {
  // ── Identification ────────────────────────────────────────────────────
  id        String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  auctionId String @map("auction_id") @db.Uuid
  brokerId  String @map("broker_id") @db.Uuid

  // ── Bid details ──────────────────────────────────────────────────────
  amount     Decimal
  documentId String? @map("document_id") @db.Uuid

  // ── Relations ────────────────────────────────────────────────────────
  auction  Auction        @relation("AuctionBids", fields: [auctionId], references: [id], onDelete: Cascade)
  broker   BrokerProfile  @relation("BrokerBids", fields: [brokerId], references: [id])
  document Documentation? @relation("BidDocument", fields: [documentId], references: [id])
  winner   AuctionWinner?

  // ── Timestamps ───────────────────────────────────────────────────────
  createdAt DateTime @default(now()) @map("created_at")

 // ── Indexes ──────────────────────────────────────────────────────────
  @@index([documentId])
  @@map("bid")
  @@schema("public")
}

/// @description Records the winning brokers selected by account holders (up to 3 per auction).
model AuctionWinner {
  // ── Identification ────────────────────────────────────────────────────
  id        String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  auctionId String @map("auction_id") @db.Uuid
  brokerId  String @map("broker_id") @db.Uuid
  bidId     String @unique @map("bid_id") @db.Uuid

  // ── Ranking & Status ─────────────────────────────────────────────────
  position              Int
  selectedAt            DateTime  @default(now()) @map("selected_at")
  contactDataRevealedAt DateTime? @map("contact_data_revealed_at")

  // ── Relations ────────────────────────────────────────────────────────
  auction    Auction            @relation(fields: [auctionId], references: [id], onDelete: Cascade)
  broker     BrokerProfile      @relation(fields: [brokerId], references: [id])
  bid        Bid                @relation(fields: [bidId], references: [id])
  commission AuctionCommission?

 // ── Indexes ──────────────────────────────────────────────────────────
  @@unique([auctionId, position])
  @@map("auction_winner")
  @@schema("public")
}

/// @description Manages lead payments made by brokers (10% of policy premium) to access account holder contact data.
model AuctionCommission {
  // ── Identification ────────────────────────────────────────────────────
  id        String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  auctionId String @map("auction_id") @db.Uuid
  winnerId  String @unique @map("winner_id") @db.Uuid
  brokerId  String @map("broker_id") @db.Uuid

  // ── Payment info ─────────────────────────────────────────────────────
  amount                Decimal
  status                String
  stripePaymentIntentId String?   @unique @map("stripe_payment_intent_id")
  paidAt                DateTime? @map("paid_at")

  // ── Relations ────────────────────────────────────────────────────────
  auction Auction       @relation(fields: [auctionId], references: [id])
  winner  AuctionWinner @relation(fields: [winnerId], references: [id])
  broker  BrokerProfile @relation(fields: [brokerId], references: [id])

  // ── Timestamps ───────────────────────────────────────────────────────
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

 // ── Indexes ──────────────────────────────────────────────────────────
  @@map("auction_commission")
  @@schema("public")
}
