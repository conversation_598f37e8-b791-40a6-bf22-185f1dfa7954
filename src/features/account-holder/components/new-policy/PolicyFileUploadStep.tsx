"use client";

import { useState, useCallback, useRef, useMemo } from "react";
import { ArrowR<PERSON>, ArrowLeft } from "lucide-react";
import Link from "next/link";
import { FileUpload } from "@/components/ui/file-upload";
import { useToast } from "@/components/ui/use-toast";
import { useSidebar } from "@/components/ui/sidebar";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";

interface PolicyFileUploadStepProps {
  onBack: () => void;
  onContinue: () => void;
  onFileSelect?: (file: File) => void;
  isSubmitting?: boolean;
}

export function PolicyFileUploadStep({
  onBack,
  onContinue,
  onFileSelect,
  isSubmitting = false,
}: PolicyFileUploadStepProps) {
  const { state } = useSidebar();
  const { toast } = useToast();

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [extractionError, setExtractionError] = useState<string | null>(null);
  const [isFileValid, setIsFileValid] = useState(false);

  const extractionToastShownRef = useRef(false);

  const errorMessages: { [key: string]: string } = {
    UNSUPPORTED_DOCUMENT_TYPE:
      "El archivo no parece ser una póliza de seguro. Por favor, asegúrate de subir el documento oficial de tu póliza, que normalmente incluye el número de póliza, las fechas de vigencia y las coberturas.",
    ILLEGIBLE_DOCUMENT:
      "No pudimos leer el texto del documento. Por favor, intenta con una foto o escaneo que sea más nítido y esté bien iluminado.",
    GENERIC_VALIDATION_FAILURE:
      "El documento que has subido no es una póliza de seguro válida o no es legible. Por favor, inténtalo de nuevo con un documento correcto.",
  };

  const handleFileSelect = useCallback(
    async (file: File) => {
      setSelectedFile(file);
      setExtractionError(null);
      setIsProcessing(true);

      try {
        // Call the parent component's onFileSelect if provided
        onFileSelect?.(file);

        // TODO: Enable API extraction in Phase 2
        // For now, simulate successful validation
        // const formData = new FormData();
        // formData.append("file", file);

        // const response = await fetch("/api/policies/extract", {
        //   method: "POST",
        //   body: formData,
        // });

        // const result = await response.json();

        // if (!response.ok || result.isValid === false) {
        //   const errorCode = result.errorCode || "GENERIC_VALIDATION_FAILURE";
        //   const userFacingError = errorMessages[errorCode] || errorMessages.GENERIC_VALIDATION_FAILURE;
        //   setExtractionError(userFacingError);
        //   toast({
        //     variant: "destructive",
        //     title: "Validación Fallida",
        //     description: "Por favor, revisa el error e inténtalo de nuevo.",
        //   });
        //   return;
        // }

        // Simulate successful extraction for UI phase

        setIsFileValid(true);
        extractionToastShownRef.current = true;
        setIsProcessing(false);
      } catch (error: any) {
        console.error("Error during file processing:", error);
        const errorMessage =
          error.message || "No se pudo procesar el documento.";
        setExtractionError(errorMessage);
        toast({
          variant: "destructive",
          title: "Error en la extracción",
          description:
            "Ocurrió un error inesperado. Por favor, inténtelo de nuevo.",
        });
        setIsProcessing(false);
      }
    },
    [onFileSelect, toast]
  );

  const handleFileRemove = useCallback(() => {
    setSelectedFile(null);
    setIsFileValid(false);
    setIsProcessing(false);
    setExtractionError(null);
    extractionToastShownRef.current = false;
  }, []);

  const handleContinue = useCallback(() => {
    console.log('Button clicked!', { canContinue, isFileValid, acceptTerms, isProcessing });
    if (!isFileValid || !acceptTerms || isProcessing || isSubmitting) return;
    onContinue();
  }, [isFileValid, acceptTerms, isProcessing, isSubmitting, onContinue]);



  const canContinue = useMemo(
    () => isFileValid && acceptTerms && !isProcessing && !isSubmitting,
    [isFileValid, acceptTerms, isProcessing, isSubmitting]
  );

  return (
    <>
      <div className="flex flex-col space-y-6 pb-24">
        <FileUpload
          onFileSelect={handleFileSelect}
          onFileRemove={handleFileRemove}
          isProcessing={isProcessing}
          processingText="Subiendo tu póliza..."
          error={extractionError}
          onErrorClear={() => setExtractionError(null)}
          title="Arrastra tu PDF o imagen, o haz clic para seleccionar un archivo"
          description="Aceptamos PDF, JPG, PNG. Máx 10 MB"
          acceptedFileTypes={["application/pdf", "image/jpeg", "image/png"]}
          maxFileSize={10 * 1024 * 1024} // 10MB
        />

        <div className="flex items-center justify-center space-x-2 mt-4">
          <Checkbox
            id="acceptTerms"
            name="acceptTerms"
            checked={acceptTerms}
            onCheckedChange={(checked) => setAcceptTerms(checked as boolean)}
            required
          />
          <label htmlFor="acceptTerms" className="text-sm text-muted-foreground">
            He leído y acepto los{" "}
            <Link
              href="https://zeeguros.com/terminos-condiciones/"
              target="_blank"
              rel="noopener noreferrer"
              className="underline underline-offset-4 hover:text-primary"
            >
              términos y condiciones
            </Link>{" "}
            de Zeeguros y que me contactéis con mis ofertas personalizadas.
            <span className="text-red-500">*</span>
          </label>
        </div>
      </div>

      {/* Fixed Footer Navigation - Simplified structure */}
      <div
        className={`fixed bottom-0 left-0 right-0 z-[150] border-t bg-background shadow-lg ${
          state === "collapsed"
            ? "md:left-[calc(var(--sidebar-width-icon)_+_1rem)]"
            : "md:left-[var(--sidebar-width)]"
        }`}
      >
        <div className="flex h-16 items-center justify-between px-4">
          <Button
            type="button"
            variant="outline"
            onClick={onBack}
            className="hover:bg-primary hover:border-primary hover:text-primary-foreground"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Atrás
          </Button>

          <Button
            type="button"
            className={
              !canContinue
                ? "cursor-not-allowed bg-gray-300 hover:bg-gray-300 border-gray-400"
                : "bg-primary hover:bg-primary/90 text-primary-foreground"
            }
            disabled={!canContinue || isProcessing || isSubmitting}
            onClick={handleContinue}
            onMouseEnter={() => console.log('Button mouse enter')}
            onMouseLeave={() => console.log('Button mouse leave')}
          >
            Confirmar y crear subasta
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </>
  );
}
