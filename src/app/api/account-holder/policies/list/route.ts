import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/features/auth/utils/server-auth";
import { db } from "@/lib/db";
import { z } from "zod";
import { transformPolicyInsuredPartiesData } from "@/features/policies/utils/insured-party-transformer";

// Validation schema for query parameters
const listPoliciesSchema = z.object({
  page: z.string().optional().default("1").transform(Number),
  limit: z.string().optional().default("10").transform(Number),
  status: z.string().optional(),
  assetType: z.string().optional(),
  search: z.string().optional(),
});

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user using the proper server auth method
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: "No autorizado" },
        { status: 401 }
      );
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validationResult = listPoliciesSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Parámetros de consulta inválidos", details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { page, limit, status, assetType, search } = validationResult.data;

    // Calculate pagination
    const skip = (page - 1) * limit;

    // First, get the account holder profile for this user
    const accountHolderProfile = await db.accountHolderProfile.findUnique({
      where: { userId: user.id }
    });

    if (!accountHolderProfile) {
      return NextResponse.json(
        { error: "Perfil de titular de cuenta no encontrado" },
        { status: 404 }
      );
    }

    // Build where clause for filtering
    const whereClause: any = {
      accountHolderId: accountHolderProfile.id, // Ensure RLS - only user's own policies
    };

    // Add status filter if provided
    if (status && status !== "all") {
      if (status === "attention") {
        whereClause.status = {
          in: ["RENEW_SOON", "EXPIRED"]
        };
      } else {
        whereClause.status = status;
      }
    }

    // Add asset type filter if provided
    if (assetType && assetType !== "all") {
      whereClause.asset = {
        assetType: assetType
      };
    }

    // Add search filter if provided
    if (search) {
      whereClause.OR = [
        {
          policyNumber: {
            contains: search,
            mode: "insensitive"
          }
        },
        {
          productName: {
            contains: search,
            mode: "insensitive"
          }
        },

        {
          asset: {
            vehicleDetails: {
              OR: [
                {
                  brand: {
                    contains: search,
                    mode: "insensitive"
                  }
                },
                {
                  model: {
                    contains: search,
                    mode: "insensitive"
                  }
                }
              ]
            }
          }
        }
      ];
    }

    // Query policies with relations
    const [policies, totalCount] = await Promise.all([
      db.policy.findMany({
        where: whereClause,
        include: {
          asset: {
            include: {
              vehicleDetails: {
                select: {
                  licensePlate: true,
                  firstRegistrationDate: true,
                  brand: true,
                  model: true,
                  version: true,
                  year: true,
                  fuelType: true,
                  chassisNumber: true,
                  powerCv: true,
                  seats: true,
                  usageType: true,
                  garageType: true,
                  kmPerYear: true,
                  isLeased: true
                }
              }
            }
          },
          accountHolder: {
            include: {
              user: {
                select: {
                  firstName: true,
                  lastName: true
                }
              }
            }
          },
          coverages: true,
          insuredParties: {
            include: {
              insuredParty: true
            }
          }
        },
        orderBy: {
          updatedAt: "desc"
        },
        skip,
        take: limit,
      }),
      db.policy.count({
        where: whereClause,
      })
    ]);

    // Transform data for frontend
    const transformedPolicies = policies.map((policy) => ({
      id: policy.id,
      policyNumber: policy.policyNumber,
      status: policy.status,
      vehicleType: policy.asset?.assetType || "UNKNOWN",
      startDate: policy.startDate,
      endDate: policy.endDate,
      premium: policy.premium,
      productName: policy.productName,
      insurerCompany: policy.insurerCompany,
      asset: policy.asset ? {
        id: policy.asset.id,
        assetType: policy.asset.assetType,
        description: policy.asset.description,
        value: policy.asset.value,
        vehicleDetails: policy.asset.vehicleDetails ? {
          licensePlate: policy.asset.vehicleDetails.licensePlate,
          firstRegistrationDate: policy.asset.vehicleDetails.firstRegistrationDate,
          brand: policy.asset.vehicleDetails.brand,
          model: policy.asset.vehicleDetails.model,
          version: policy.asset.vehicleDetails.version,
          year: policy.asset.vehicleDetails.year,
          fuelType: policy.asset.vehicleDetails.fuelType,
          chassisNumber: policy.asset.vehicleDetails.chassisNumber,
          powerCv: policy.asset.vehicleDetails.powerCv,
          seats: policy.asset.vehicleDetails.seats,
          usageType: policy.asset.vehicleDetails.usageType,
          garageType: policy.asset.vehicleDetails.garageType,
          kmPerYear: policy.asset.vehicleDetails.kmPerYear,
          isLeased: policy.asset.vehicleDetails.isLeased
        } : null
      } : null,
      insuredParties: policy.insuredParties ? transformPolicyInsuredPartiesData(policy.insuredParties) : [],
      accountHolder: policy.accountHolder ? {
        id: policy.accountHolder.id,
        userId: policy.accountHolder.userId,
        firstName: policy.accountHolder.user.firstName,
        lastName: policy.accountHolder.user.lastName
      } : null,
      coverages: policy.coverages || []
    }));

    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return NextResponse.json({
      data: transformedPolicies,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      }
    });

  } catch (error) {
    console.error("Error fetching policies:", error);
    
    // Log more detailed error information
    if (error instanceof Error) {
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);
    }
    
    return NextResponse.json(
      { 
        error: "Error interno del servidor",
        details: process.env.NODE_ENV === "development" ? error instanceof Error ? error.message : String(error) : undefined
      },
      { status: 500 }
    );
  }
}