import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { sanitizeFilename } from "@/lib/utils";
import { uploadToR2 } from "../extract/route";
import { db } from "@/lib/db";
import {
  GoogleGenerativeAI,
  Part,
  GenerationConfig,
} from "@google/generative-ai";
import { zodToJsonSchema } from "zod-to-json-schema";
import { z } from "zod";
import {
  AssetsType,
  FuelType,
  UsageType,
  GarageType,
  KmRange,
  PartyRole,
  GuaranteeType,
  PaymentPeriod,
  PolicyType,
} from "@prisma/client";

// Initialize the Gemini API client
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || "");

const ValidationSchema = z.object({
  isValid: z
    .boolean()
    .describe("Indicates if the document is a valid insurance policy."),
  reason: z.string().describe("A concise reason for the validation decision."),
});

const createPolicySchema = z.object({
  type: z.enum(["CAR", "MOTORCYCLE"]),
  file: z.any().optional(), // Use z.any() for File objects in server environment
});

// This schema is now more lenient, allowing null values for fields that Gemini might not find.
// This prevents validation failures and allows for partial data to be returned to the frontend.
const ExtractionSchema = z.object({
  policyNumber: z.string().min(1).nullable().describe("The policy number."),
  productName: z
    .string()
    .nullable()
    .describe("The commercial name of the insurance product."),
  startDate: z.coerce
    .date()
    .nullable()
    .describe("The start date of the policy coverage (YYYY-MM-DD)."),
  endDate: z.coerce
    .date()
    .nullable()
    .describe("The end date of the policy coverage (YYYY-MM-DD)."),
  paymentPeriod: z
    .nativeEnum(PaymentPeriod)
    .nullable()
    .describe("The frequency of premium payments."),
  policyType: z
    .nativeEnum(PolicyType)
    .nullable()
    .describe("The type of policy."),
  premium: z.number().nullable().describe("The cost of the insurance premium."),
  insurerName: z
    .string()
    .nullable()
    .describe("The full name of the insurance company."),
  asset: z
    .object({
      licensePlate: z
        .string()
        .nullable()
        .describe("The asset's license plate."),
      brand: z.string().nullable().describe("The brand of the asset."),
      model: z.string().nullable().describe("The model of the asset."),
      year: z
        .number()
        .nullable()
        .describe("The manufacturing year of the asset."),
      chassisNumber: z
        .string()
        .nullable()
        .describe("The asset's chassis number (VIN)."),
      firstRegistrationDate: z.coerce
        .date()
        .nullable()
        .describe("The date the asset was first registered (YYYY-MM-DD)."),
      type: z.nativeEnum(AssetsType).nullable().describe("The type of asset."),
      fuelType: z
        .nativeEnum(FuelType)
        .nullable()
        .describe("The type of fuel the asset uses."),
      powerCv: z
        .number()
        .nullable()
        .describe("The asset's power in horsepower (CV)."),
      seats: z
        .number()
        .nullable()
        .describe("The number of seats in the asset."),
      usageType: z
        .nativeEnum(UsageType)
        .nullable()
        .describe("The primary use of the asset."),
      garageType: z
        .nativeEnum(GarageType)
        .nullable()
        .describe("The type of garage where the asset is parked."),
      kmPerYear: z
        .nativeEnum(KmRange)
        .nullable()
        .describe("The estimated kilometers driven per year."),
    })
    .describe("Details of the insured asset."),
  insuredParties: z
    .array(
      z.object({
        fullName: z.string().describe("The full name of the insured person."),
        dni: z.string().describe("The DNI/NIE of the insured person."),
        role: z
          .nativeEnum(PartyRole)
          .describe(
            "The role of the person in the policy (e.g., POLICYHOLDER)."
          ),
      })
    )
    .describe("A list of all parties insured under the policy."),
  coverages: z
    .array(
      z.object({
        type: z
          .nativeEnum(GuaranteeType)
          .nullable()
          .describe("The type of coverage."),
        customName: z
          .string()
          .optional()
          .nullable()
          .describe("A custom name if the type is 'OTHER'."),
        limit: z
          .number()
          .optional()
          .nullable()
          .describe("The financial limit of the coverage."),
        deductible: z
          .number()
          .optional()
          .nullable()
          .describe("The deductible amount for the coverage."),
        description: z
          .string()
          .optional()
          .nullable()
          .describe("A brief description of the coverage."),
      })
    )
    .describe("A list of all coverages included in the policy."),
});

// Maps the raw AI reason to a safe, standardized error code.
function mapReasonToErrorCode(reason: string): string {
  const lowerCaseReason = reason.toLowerCase();
  if (
    lowerCaseReason.includes("invoice") ||
    lowerCaseReason.includes("factura")
  ) {
    return "UNSUPPORTED_DOCUMENT_TYPE";
  }
  if (
    lowerCaseReason.includes("illegible") ||
    lowerCaseReason.includes("blurry") ||
    lowerCaseReason.includes("borroso")
  ) {
    return "ILLEGIBLE_DOCUMENT";
  }
  if (
    lowerCaseReason.includes("not a policy") ||
    lowerCaseReason.includes("no es una póliza") ||
    lowerCaseReason.includes("person") ||
    lowerCaseReason.includes("human") ||
    lowerCaseReason.includes("persona")
  ) {
    return "UNSUPPORTED_DOCUMENT_TYPE";
  }
  return "GENERIC_VALIDATION_FAILURE"; // Default fallback
}

export async function POST(request: NextRequest) {
  try {
    // Get the user session server-side
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "No autorizado" }, { status: 401 });
    }

    // Parse the form data
    const formData = await request.formData();
    const type = formData.get("type") as AssetsType;
    const file = formData.get("file") as File | null;

    const validationResult = createPolicySchema.safeParse({
      type,
      file: file || undefined,
    });

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Datos inválidos", details: validationResult.error.errors },
        { status: 400 }
      );
    }

    if (file && !(file instanceof File)) {
      return NextResponse.json(
        { error: "El archivo proporcionado no es válido" },
        { status: 400 }
      );
    }

    let documentPath = null;
    let policyId = null;

    if (file) {
      const userId = user.id;
      
      // Get the user's AccountHolderProfile
      const accountHolderProfile = await db.accountHolderProfile.findUnique({
        where: { userId },
      });

      if (!accountHolderProfile) {
        return NextResponse.json(
          { error: "Perfil de titular de cuenta no encontrado" },
          { status: 404 }
        );
      }

      const sanitizedFilename = sanitizeFilename(file.name);
      const filePath = `${userId}/${Date.now()}_${sanitizedFilename}`;

      const r2Key = await uploadToR2(file);

      if (!r2Key) {
        return NextResponse.json(
          { error: "Error subiendo documento" },
          { status: 500 }
        );
      }

      // First create the Documentation record
      const documentation = await db.documentation.create({
        data: {
          accountHolderId: accountHolderProfile.id,
          type: "POLICY_DOCUMENT",
          url: r2Key,
          fileName: sanitizedFilename,
          fileSize: file.size,
          mimeType: file.type,
        },
      });

      // Create the Asset record first
      const asset = await db.asset.create({
        data: {
          accountHolderId: accountHolderProfile.id,
          assetType: type,
          description: `${type === "CAR" ? "Coche" : "Motocicleta"} - Pendiente de completar información`,
        },
      });

      // Create the Vehicle record linked to the Asset
      await db.vehicle.create({
        data: {
          assetId: asset.id,
          // Initial creation with minimal data - will be populated by AI extraction
        },
      });

      // Then create the Policy record linking to both Documentation and Asset
      const newPolicy = await db.policy.create({
        data: {
          accountHolder: {
            connect: {
              id: accountHolderProfile.id,
            },
          },
          document: {
            connect: {
              id: documentation.id,
            },
          },
          asset: {
            connect: {
              id: asset.id,
            },
          },
          isAssetsTypeConfirmed: true,
          status: "DRAFT",
        },
      });
      documentPath = filePath;
      policyId = newPolicy.id;
    }

    if (policyId && file) {
      await populatePolicy(policyId, file);
    }

    return NextResponse.json({
      success: true,
      message: "Póliza registrada con éxito",
      documentPath,
      policyId,
      assetType: type,
    });
  } catch (error) {
    console.error("Policy creation error:", error);
    return NextResponse.json(
      { error: "Error interno del servidor" },
      { status: 500 }
    );
  }
}

async function populatePolicy(policyId: any, file: File) {
  try {
    const modelName = process.env.GEMINI_MODEL || "gemini-1.5-flash";
    const model = genAI.getGenerativeModel({ model: modelName });

    const generationConfig: GenerationConfig = {
      responseMimeType: "application/json",
    };

    // Stage 1: "Bodyguard" Validation
    const validationJsonSchema = zodToJsonSchema(
      ValidationSchema,
      "validationSchema"
    );
    const validationPrompt = `You are a strict validation gateway for an insurance policy upload wizard. Your primary function is to be a 'bodyguard'. You must rigorously analyze the document and determine with high confidence if it is a valid and legible insurance policy.

    Follow these rules strictly:
    1.  **Analyze Content:** Look for key identifiers like policy number, insured name, insurer, coverage types, and effective dates.
    2.  **Strict Rejection:** If the document is NOT a valid policy (e.g., it's an invoice, a random photo, a blank page, heavily corrupted, or completely illegible), you MUST return \`"isValid": false\`.
    3.  **Return ONLY JSON:** Your entire output must be a single JSON object conforming to the provided schema.

    JSON Schema:
    ${JSON.stringify(validationJsonSchema, null, 2)}
    `;

    const imagePart: Part = {
      inlineData: {
        mimeType: file.type,
        data: Buffer.from(await file.arrayBuffer()).toString("base64"),
      },
    };

    const geminiValidationResult = await model.generateContent({
      contents: [
        { role: "user", parts: [imagePart, { text: validationPrompt }] },
      ],
      generationConfig,
    });

    const validationResponseText = geminiValidationResult.response.text();
    const validationData = JSON.parse(validationResponseText);
    const parsedValidation = ValidationSchema.safeParse(validationData);

    if (!parsedValidation.success || !parsedValidation.data.isValid) {
      const reason = parsedValidation.success
        ? parsedValidation.data.reason
        : "Invalid validation response from AI.";

      const errorCode = mapReasonToErrorCode(reason);
      return;
    }

    // Stage 2: Data Extraction
    const extractionJsonSchema = zodToJsonSchema(
      ExtractionSchema,
      "extractionSchema"
    );
    const extractionPrompt = `You are an expert insurance data extractor for the Spanish market. The document has already been validated as an insurance policy. Now, analyze the provided policy document and return ONLY a single, clean JSON object that conforms to the following JSON schema.
      - If a value cannot be found, use null.
      - Dates must be in YYYY-MM-DD format.
      - For ENUM fields, use the exact uppercase values provided in the schema.

      JSON Schema:
      ${JSON.stringify(extractionJsonSchema, null, 2)}
      `;

    const extractionResult = await model.generateContent({
      contents: [
        { role: "user", parts: [imagePart, { text: extractionPrompt }] },
      ],
      generationConfig,
    });

    const responseText = extractionResult.response.text(); 
    console.log("🚀 ~ populatePolicy ~ responseText:", responseText)
    const extractedData = JSON.parse(responseText);

    // Transform coverages to handle non-enum values
    if (extractedData.coverages && Array.isArray(extractedData.coverages)) {
      const validGuaranteeTypes = Object.keys(GuaranteeType);
      extractedData.coverages.forEach((coverage: any) => {
        if (coverage.type && !validGuaranteeTypes.includes(coverage.type)) {
          console.warn(
            `Invalid GuaranteeType '${coverage.type}' received from AI. Mapping to 'OTHER'.`
          );
          coverage.customName = coverage.type; // Preserve original value
          coverage.type = "OTHER";
        }
      });
    }

    const extractionValidationResult =
      ExtractionSchema.safeParse(extractedData);

    if (!extractionValidationResult.success) {
      console.error(
        "Zod validation failed:",
        extractionValidationResult.error.flatten()
      );
      return;
    }

    let data: any = extractionValidationResult.data;
    
    // Extract vehicle data from the AI response
    const vehicleData = data.asset;
    
    // Remove asset data from policy data since it will be stored in Vehicle table
    delete data.asset;
    
    // Update the Policy record
    await db.policy.update({
      where: { id: policyId },
      data,
    });

    // Update the Vehicle record with extracted data
    if (vehicleData) {
      // Get the policy with its asset to find the vehicle
      const policy = await db.policy.findUnique({
        where: { id: policyId },
        include: {
          asset: {
            include: {
              vehicleDetails: true
            }
          }
        }
      });

      if (policy?.asset?.vehicleDetails) {
        await db.vehicle.update({
          where: { id: policy.asset.vehicleDetails.id },
          data: {
            licensePlate: vehicleData.licensePlate,
            brand: vehicleData.brand,
            model: vehicleData.model,
            year: vehicleData.year,
            chassisNumber: vehicleData.chassisNumber,
            firstRegistrationDate: vehicleData.firstRegistrationDate,
            fuelType: vehicleData.fuelType,
            powerCv: vehicleData.powerCv,
            seats: vehicleData.seats,
            usageType: vehicleData.usageType,
            garageType: vehicleData.garageType,
            kmPerYear: vehicleData.kmPerYear,
          },
        });
      }
    }
  } catch (error: unknown) {
    console.error("Error in extraction API:", error);
  }
}
