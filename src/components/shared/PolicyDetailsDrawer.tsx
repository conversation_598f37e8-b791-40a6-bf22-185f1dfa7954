"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>er<PERSON>ontent } from "@/components/shared/drawer";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { CoverageCard } from "@/features/auctions/components/coverage-card";
import { useSendOffer } from "@/features/auctions/hooks/use-send-offer";
import { usePolicyInsuredParties } from "@/features/policies/hooks/usePolicyInsuredParties";
import { cn } from "@/lib/utils";
import { maskPolicyNumber, maskName, maskLicensePlate } from "@/lib/utils";
import { formatAssetType } from "@/lib/format-asset-type";
import { Upload, FileText } from "lucide-react";
import { CheckCircle } from "lucide-react";
import { PolicyDetailsDrawerProps } from "@/types/policy";
import { translatePartyRole, translateGender } from "@/features/policies/utils/translations";
import { PartyRole, Gender } from "@prisma/client";

export function PolicyDetailsDrawer({
  isOpen,
  onClose,
  mode = "broker", // Default to broker mode for backward compatibility
  policyData,
}: PolicyDetailsDrawerProps) {
  const [annualPremium, setAnnualPremium] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  const sendOfferMutation = useSendOffer();

  // Fetch insured parties data when drawer is open and policy ID is available
  const { data: insuredParties, isLoading: isLoadingInsuredParties } = usePolicyInsuredParties(
    isOpen && policyData?.id ? policyData.id : null
  );

  const handleFileSelect = (file: File) => {
    // Validate file type and size
    const allowedTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ];
    const maxSize = 10 * 1024 * 1024; // 10MB

    if (!allowedTypes.includes(file.type)) {
      alert("Solo se permiten archivos PDF, DOC y DOCX");
      return;
    }

    if (file.size > maxSize) {
      alert("El archivo no puede superar los 10MB");
      return;
    }

    setSelectedFile(file);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0 && files[0]) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0 && files[0]) {
      handleFileSelect(files[0]);
    }
  };

  const handleSendOffer = () => {
    if (!annualPremium || !policyData) {
      alert("Por favor, introduce la prima anual");
      return;
    }

    setShowConfirmDialog(true);
  };

  const handleConfirmSend = async () => {
    if (!policyData) return;

    try {
      await sendOfferMutation.mutateAsync({
        policyId: policyData.id,
        annualPremium: parseFloat(annualPremium),
        // TODO: Upload file and get URL
        fileUrl: selectedFile ? "https://example.com/file.pdf" : undefined,
      });

      setShowConfirmDialog(false);
      onClose();
      setAnnualPremium("");
      setSelectedFile(null);
    } catch (error) {
      console.error("Error sending offer:", error);
      alert("Error al enviar la oferta. Por favor, inténtalo de nuevo.");
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "DISPONIBLE":
        return "bg-[#3AE386] text-black";
      case "PARTICIPANDO":
        return "bg-[#3AE386] text-black";
      case "GANADA":
        return "bg-[#3AE386] text-black";
      case "CONFIRMADA":
      case "DEAL_CONFIRMED":
        return "bg-[#3AE386] text-black";
      case "PERDIDAS":
      case "EXPIRED":
      case "CANCELED":
      case "CLOSED":
        return "bg-gray-500 text-black";
      default:
        return "bg-gray-500 text-white";
    }
  };

  const getStatusDisplayText = (status: string) => {
    switch (status) {
      case "CONFIRMADA":
      case "DEAL_CONFIRMED":
        return "CONFIRMADA";
      case "PERDIDAS":
        return "PERDIDA";
      case "EXPIRED":
        return "EXPIRADA";
      case "CANCELED":
        return "CANCELADA";
      case "CLOSED":
        return "CERRADA";
      default:
        return status;
    }
  };

  // Determine if data should be masked based on mode and status
  const shouldMaskData = mode === "broker" && !(policyData?.status === "CONFIRMADA" || policyData?.status === "DEAL_CONFIRMED");

  // Determine if broker actions should be shown
  const showBrokerActions = mode === "broker";
  const offerDisabledStatuses = ["CONFIRMADA", "DEAL_CONFIRMED", "FINALIZADA", "PERDIDAS", "EXPIRED", "CANCELED", "CLOSED"];

  if (!policyData) return null;

  return (
    <>
      <Drawer isOpen={isOpen} onClose={onClose}>
        <DrawerHeader onClose={onClose}>
          <div className="flex items-center gap-3">
            <h4 className="text-xl font-semibold text-gray-900">
              {mode === "account-holder" ? "Detalles de la Póliza" : "Detalles de la Subasta"}
            </h4>
            <Badge
              className={cn(
                "text-sm font-medium",
                getStatusBadgeColor(policyData.status)
              )}
            >
              {getStatusDisplayText(policyData.status)}
            </Badge>
          </div>
        </DrawerHeader>

        <DrawerContent className="p-0">
          <Accordion type="multiple" defaultValue={mode === "broker" ? ["offer"] : ["info"]} className="w-full">
              {/* Información de Póliza */}
              <AccordionItem value="info" className="border-b px-4 sm:px-6">
                <AccordionTrigger className="text-base font-medium no-underline hover:no-underline">
                  📄 Información de Póliza
                </AccordionTrigger>
                <AccordionContent className="space-y-3">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm sm:text-base">
                    <div>
                      <span className="text-gray-600">Número de Póliza:</span>
                      <div className="font-medium break-all">
                        {shouldMaskData ? maskPolicyNumber(policyData.policyNumber) : policyData.policyNumber}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-600">Aseguradora:</span>
                      <div className="font-medium">{policyData.insurer}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Producto:</span>
                      <div className="font-medium">{policyData.product}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Tipo de Póliza:</span>
                      <div className="font-medium">{formatAssetType(policyData.policyType)}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Vigencia:</span>
                      <div className="font-medium">{policyData.validity}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Prima Anual:</span>
                      <div className="font-medium">{policyData.annualPremium}</div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Partes Aseguradas - Datos protegidos solo en modo broker */}
              <AccordionItem value="insured-parties" className="border-b px-4 sm:px-6">
                <AccordionTrigger className="text-base font-medium no-underline hover:no-underline">
                  <div className="flex items-center gap-2">
                    <span>👥 Partes Aseguradas</span>
                    {shouldMaskData && (
                      <Badge className="bg-red-500 text-white text-xs hover:bg-red-500 hover:text-white">
                        Datos protegidos
                      </Badge>
                    )}
                  </div>
                </AccordionTrigger>
                <AccordionContent className="space-y-3">
                  {isLoadingInsuredParties ? (
                    <div className="text-sm sm:text-base text-gray-600">
                      Cargando partes aseguradas...
                    </div>
                  ) : insuredParties && insuredParties.length > 0 ? (
                    <div className="space-y-4">
                      <div className="text-sm sm:text-base text-gray-600 mb-4">
                        Total: {insuredParties.length} parte{insuredParties.length !== 1 ? 's' : ''} asegurada{insuredParties.length !== 1 ? 's' : ''}
                      </div>
                      {insuredParties.map((party) => (
                        <div key={party.id} className="border rounded-lg p-4 bg-gray-50">
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm sm:text-base">
                            <div>
                              <span className="text-gray-600">Nombre:</span>
                              <div className="font-medium">
                                {shouldMaskData ? maskName(party.fullName) : party.fullName}
                              </div>
                            </div>
                            <div>
                              <span className="text-gray-600">Identificación:</span>
                              <div className="font-medium">
                                {shouldMaskData ? '*********' : party.identification}
                              </div>
                            </div>
                            <div>
                              <span className="text-gray-600">Rol:</span>
                              <div className="font-medium">
                                {translatePartyRole(party.role as PartyRole)}
                              </div>
                            </div>
                            {party.gender && (
                              <div>
                                <span className="text-gray-600">Género:</span>
                                <div className="font-medium">{translateGender(party.gender as Gender)}</div>
                              </div>
                            )}
                            {party.birthDate && (
                              <div>
                                <span className="text-gray-600">Fecha de Nacimiento:</span>
                                <div className="font-medium">
                                  {new Date(party.birthDate).toLocaleDateString('es-ES')}
                                </div>
                              </div>
                            )}
                            {party.driverLicenseNumber && (
                              <div>
                                <span className="text-gray-600">Licencia de Conducir:</span>
                                <div className="font-medium">
                                  {shouldMaskData ? '*********' : party.driverLicenseNumber}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-sm sm:text-base text-gray-600">
                      No hay partes aseguradas registradas para esta póliza.
                    </div>
                  )}
                </AccordionContent>
              </AccordionItem>

              {/* Vehículo */}
              <AccordionItem value="vehicle" className="border-b px-4 sm:px-6">
                <AccordionTrigger className="text-base font-medium no-underline hover:no-underline">
                  🚗 Vehículo
                </AccordionTrigger>
                <AccordionContent className="space-y-3">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm sm:text-base">
                    <div>
                      <span className="text-gray-600">Matrícula:</span>
                      <div className="font-medium">
                        {shouldMaskData ? maskLicensePlate(policyData.vehiclePlate) : policyData.vehiclePlate}
                      </div>
                    </div>
                    {policyData.vehicleFirstRegistrationDate && (
                      <div>
                        <span className="text-gray-600">Fecha de 1ª matriculación:</span>
                        <div className="font-medium">
                          {new Date(policyData.vehicleFirstRegistrationDate).toLocaleDateString('es-ES')}
                        </div>
                      </div>
                    )}
                    <div>
                      <span className="text-gray-600">Marca:</span>
                      <div className="font-medium">{policyData.vehicleBrand}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Modelo:</span>
                      <div className="font-medium">{policyData.vehicleModel}</div>
                    </div>
                    {policyData.vehicleVersion && (
                      <div>
                        <span className="text-gray-600">Versión:</span>
                        <div className="font-medium">{policyData.vehicleVersion}</div>
                      </div>
                    )}
                    {policyData.vehicleManufacturingYear && (
                      <div>
                        <span className="text-gray-600">Año de fabricación:</span>
                        <div className="font-medium">{policyData.vehicleManufacturingYear}</div>
                      </div>
                    )}
                    <div>
                      <span className="text-gray-600">Tipo de Vehículo:</span>
                      <div className="font-medium">{policyData.vehicleType}</div>
                    </div>
                    {policyData.vehicleFuelType && (
                      <div>
                        <span className="text-gray-600">Combustible:</span>
                        <div className="font-medium">{policyData.vehicleFuelType}</div>
                      </div>
                    )}
                    {policyData.vehicleVin && (
                      <div>
                        <span className="text-gray-600">Bastidor/VIN:</span>
                        <div className="font-medium">
                          {shouldMaskData ? '*********' : policyData.vehicleVin}
                        </div>
                      </div>
                    )}
                    {policyData.vehiclePower && (
                      <div>
                        <span className="text-gray-600">Potencia:</span>
                        <div className="font-medium">{policyData.vehiclePower} CV</div>
                      </div>
                    )}
                    {policyData.vehicleSeats && (
                      <div>
                        <span className="text-gray-600">Plazas:</span>
                        <div className="font-medium">{policyData.vehicleSeats}</div>
                      </div>
                    )}
                    {policyData.vehicleUsageType && (
                      <div>
                        <span className="text-gray-600">Tipo de Uso:</span>
                        <div className="font-medium">{policyData.vehicleUsageType}</div>
                      </div>
                    )}
                    {policyData.vehicleGarageType && (
                      <div>
                        <span className="text-gray-600">Tipo de Garaje:</span>
                        <div className="font-medium">{policyData.vehicleGarageType}</div>
                      </div>
                    )}
                    {policyData.vehicleKmPerYear && (
                      <div>
                        <span className="text-gray-600">KM/Año:</span>
                        <div className="font-medium">{policyData.vehicleKmPerYear}</div>
                      </div>
                    )}
                    <div>
                      <span className="text-gray-600">Vehículo Leasing:</span>
                      <div className="font-medium">{policyData.vehicleIsLeased ? 'Sí' : 'No'}</div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Coberturas */}
              <AccordionItem value="coverages" className="border-b px-4 sm:px-6">
                <AccordionTrigger className="text-base font-medium no-underline hover:no-underline">
                  🛡️ Coberturas
                </AccordionTrigger>
                <AccordionContent className="space-y-3">
                  <div className="text-sm sm:text-base text-gray-600 mb-4">
                    Total: {policyData.coverages.length} coberturas
                  </div>
                  <div className="grid gap-3">
                    {policyData.coverages.map((coverage, index) => (
                      <CoverageCard
                        key={index}
                        title={coverage.title}
                        limit={coverage.limit}
                        description={coverage.description}
                      />
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Enviar Oferta - Only show in broker mode */}
              {showBrokerActions && !offerDisabledStatuses.includes(policyData.status) ? (
                <AccordionItem value="offer" className="px-4 sm:px-6">
                  <AccordionTrigger className="text-base font-medium no-underline hover:no-underline">
                    💰 Enviar Oferta
                  </AccordionTrigger>
                  <AccordionContent className="space-y-4">
                    {policyData.status === "GANADA" ? (
                      <div className="space-y-4">
                        <div className="bg-green-50 border border-green-200 rounded-md p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <CheckCircle className="h-5 w-5 text-green-500" />
                            <p className="font-medium text-green-900">¡Has ganado esta subasta!</p>
                          </div>
                          <p className="text-sm text-gray-600">Paga la comisión para acceder a los datos completos del cliente.</p>
                        </div>
                        <Button 
                          className="w-full bg-green-500 hover:bg-green-600 text-white font-medium rounded-full"
                          // TODO: Implement payment logic
                        >
                          Pagar Comisión - €21.00
                        </Button>
                      </div>
                    ) : (
                      <>
                        <div>
                          <Label htmlFor="annual-premium" className="text-sm font-medium">
                            Prima Anual (€)
                          </Label>
                          <Input
                            id="annual-premium"
                            type="number"
                            step="0.01"
                            placeholder="Ej: 285.50"
                            value={annualPremium}
                            onChange={(e) => setAnnualPremium(e.target.value)}
                            className="mt-1"
                          />
                        </div>
                        
                        <div>
                          <Label className="text-sm font-medium">
                            Adjunta tu cotización
                          </Label>
                          <div
                            className={cn(
                              "mt-1 border-2 border-dashed rounded-lg p-4 sm:p-6 text-center transition-colors",
                              dragActive
                                ? "border-[#3AE386] bg-green-50"
                                : "border-gray-300 hover:border-gray-400"
                            )}
                            onDrop={handleDrop}
                            onDragOver={handleDragOver}
                            onDragLeave={handleDragLeave}
                          >
                            {selectedFile ? (
                              <div className="flex items-center justify-center gap-2 flex-wrap">
                                <FileText className="h-5 w-5 text-gray-500" />
                                <span className="text-sm text-gray-700 break-all">
                                  {selectedFile.name}
                                </span>
                                <button
                                  onClick={() => setSelectedFile(null)}
                                  className="text-red-500 hover:text-red-700 ml-2"
                                >
                                  ✕
                                </button>
                              </div>
                            ) : (
                              <>
                                <Upload className="h-6 w-6 sm:h-8 sm:w-8 text-gray-400 mx-auto mb-2" />
                                <p className="text-sm text-gray-600">
                                  Arrastra un archivo aquí o{" "}
                                  <label className="text-[#3AE386] hover:text-[#3EA050] cursor-pointer">
                                    selecciona uno
                                    <input
                                      type="file"
                                      className="hidden"
                                      accept=".pdf,.doc,.docx"
                                      onChange={handleFileInputChange}
                                    />
                                  </label>
                                </p>
                                <p className="text-xs text-gray-500 mt-1">
                                  PDF, DOC, DOCX (máx. 10MB)
                                </p>
                              </>
                            )}
                          </div>
                        </div>
                        
                        <Button
                          onClick={handleSendOffer}
                          disabled={!annualPremium || sendOfferMutation.isLoading}
                          className="w-full bg-[#3AE386] hover:bg-[#3EA050] text-black font-medium"
                        >
                          {sendOfferMutation.isLoading ? "Enviando..." : "Enviar Oferta"}
                        </Button>
                      </>
                    )}
                  </AccordionContent>
                </AccordionItem>
              ) : null}
            </Accordion>
        </DrawerContent>
      </Drawer>

      {/* Confirmation Dialog - Only show in broker mode */}
      {showBrokerActions && (
        <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirmar envío de oferta</AlertDialogTitle>
              <AlertDialogDescription>
                ¿Estás seguro de que quieres enviar una oferta de {annualPremium}€ anuales para esta póliza?
                {selectedFile && (
                  <span className="block mt-2">
                    Se incluirá el archivo: {selectedFile.name}
                  </span>
                )}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancelar</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleConfirmSend}
                className="bg-[#3AE386] hover:bg-[#3EA050] text-black"
              >
                Confirmar envío
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </>
  );
}